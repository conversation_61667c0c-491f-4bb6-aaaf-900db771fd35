import { useState, useCallback, useEffect } from 'react';
import { Contract, formatEther } from 'ethers';
import { CONTRACT_ADDRESSES, CREATOR_REWARD_ABI } from '@/constants';
import { useSigner } from '@/hooks/useSigner';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { errorMsg, successMsg } from '@/libs/toast';

interface CreatorPoolInfo {
  initializer: string;
  creator: string;
  totalRewards: string;
}

export const useCreatorReward = () => {
  const { signer } = useSigner();
  const { coinAddress } = useCoinPageContext();
  const [creatorPoolInfo, setCreatorPoolInfo] =
    useState<CreatorPoolInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isEligibleCreator, setIsEligibleCreator] = useState(false);
  const getCreatorPoolInfo = useCallback(async () => {
    if (!signer || !coinAddress) return null;

    try {
      setIsLoading(true);
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer,
      );

      const poolInfo = await contract.getCreatorPoolInfo(coinAddress);
      console.log(poolInfo, 'poolInfo');
      const formattedInfo: CreatorPoolInfo = {
        initializer: poolInfo[0],
        creator: poolInfo[1],
        totalRewards: formatEther(poolInfo[2]),
      };
      console.log(formattedInfo, 'formattedInfo');

      setCreatorPoolInfo(formattedInfo);

      const userAddress = await signer.getAddress();
      setIsEligibleCreator(
        formattedInfo.creator.toLowerCase() === userAddress.toLowerCase(),
      );

      return formattedInfo;
    } catch (error: any) {
      console.error('Error fetching creator pool info:', error);

      if (error?.reason === 'CreatorNotExist') {
        setCreatorPoolInfo(null);
        setIsEligibleCreator(false);
        return null;
      }

      errorMsg('Failed to fetch creator reward information');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [signer, coinAddress]);
  const claimCreatorPool = useCallback(async () => {
    if (!signer || !coinAddress || !isEligibleCreator) {
      errorMsg('You are not eligible to claim rewards for this token');
      return false;
    }

    try {
      setIsClaiming(true);
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer,
      );

      const tx = await contract.claimCreatorPool(coinAddress);
      await tx.wait();

      successMsg('Creator rewards claimed successfully!');

      await getCreatorPoolInfo();

      return true;
    } catch (error: any) {
      console.error('Error claiming creator rewards:', error);

      if (error?.reason === 'RewardToClaimNotValid') {
        errorMsg('No rewards available to claim');
      } else if (error?.reason === 'CreatorNotExist') {
        errorMsg('Creator pool does not exist for this token');
      } else if (error?.reason === 'InvalidCreator') {
        errorMsg('You are not the creator of this token');
      } else {
        errorMsg(error?.message || 'Failed to claim creator rewards');
      }

      return false;
    } finally {
      setIsClaiming(false);
    }
  }, [signer, coinAddress, isEligibleCreator, getCreatorPoolInfo]);
  const checkCreatorPoolExists = useCallback(async () => {
    if (!signer || !coinAddress) return false;

    try {
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer,
      );

      return await contract.creatorPoolExists(coinAddress);
    } catch (error) {
      console.error('Error checking creator pool existence:', error);
      return false;
    }
  }, [signer, coinAddress]);

  useEffect(() => {
    if (signer && coinAddress) {
      getCreatorPoolInfo();
    }
  }, [signer, coinAddress, getCreatorPoolInfo]);

  return {
    creatorPoolInfo,
    isLoading,
    isClaiming,
    isEligibleCreator,
    getCreatorPoolInfo,
    claimCreatorPool,
    checkCreatorPoolExists,
    hasRewards: creatorPoolInfo && parseFloat(creatorPoolInfo.totalRewards) > 0,
    rewardAmount: creatorPoolInfo?.totalRewards || '0',
  };
};
