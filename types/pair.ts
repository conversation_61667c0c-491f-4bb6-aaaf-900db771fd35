export enum TradingType {
  BUY = 'BUY',
  SELL = 'SELL',
  ADD = 'ADD',
  REMOVE = 'REMOVE',
}

export type TToken = {
  address: string;
  createdAt: string;
  decimals: number;
  isTrending: true;
  name: string;
  priceUsd: string;
  price: string;
  symbol: string;
  totalSupply: string;
  circulatingSupply?: string;
  updatedAt: string;
  volumeUsd: string;
  bannerImageUrl?: string;
  logoImageUrl?: string;
  lockAmount: string;
  lockedAmount: string;
  lockTimestamp: number;
  socials: InfoPair;
};

export type TDex = {
  dex: string;
  name: string;
  network: string;
  version: string;
};

export type TWebsite = {
  label: string;
  url: string;
};

export type TSocial = {
  type: string;
  url: string;
};

export type InfoPair = {
  imageUrl: string;
  websites: TWebsite[];
  socials: TSocial[];
};

export type TPairPrice = {
  price: string;
  priceUsd: string;
};

export type TPair = {
  createdAt: string;
  liquidity: string;
  bondingCurve: number;
  liquidityUsd: string;
  pairId: string;
  reserveBase: string;
  reserveQuote: string;
  tokenBase: TPairToken;
  tokenQuote: TPairToken;
  totalMakers: number;
  totalTxns: number;
  buyTxns: number;
  sellTxns: number;
  updateAt: string;
  volume: string;
  volumeUsd: string;
  network: string;
  slug: string;
  deployer: string;
  timestamp: number;
  dex: TDex;
  totalHolders: number;
  stats: TPairStats;
  marketCapUsd?: string;
  priceUsd?: string;
  priceSui?: string;
  graduatedSlug?: string;
  poolId: string;
  alias?: string;
  lpBurned: string | number | null;
  lpSupply: string | number | null;
  trendingRank?: string | number;
};

export type TCoinMetadata = {
  address: string;
  decimals: number;
  symbol?: string;
  name?: string;
  description?: string;
  iconUrl?: string;
  id?: string;
};

export type TPairToken = TCoinMetadata & {
  trendingRank?: number;
  createdAt: string;
  name: string;
  priceUsd: string;
  price: string;
  symbol: string;
  totalSupply: string;
  circulatingSupply?: string;
  updatedAt: string;
  volumeUsd: string;
  bannerImageUrl?: string;
  logoImageUrl?: string;
  lockAmount: string;
  lockedAmount: string;
  lockTimestamp: number;
  socials: InfoPair;
  deployerBalancePercent: number;
  freezeAuthority: boolean;
  deployer: string;
  mintAuthority: boolean;
  top10HolderPercent: number;
  amountBurned: number;
};

export type TAudit = {
  createdAt: string;
  coinDev: string;
  devBalancePercent: number;
  freezeAuthority: boolean;
  lpBurned: number;
  mintAuthority: boolean;
  top10HolderPercent: number;
  lpBurnedPercent: string;
  amountBurned: number;
  updatedAt: string;
  suspiciousActivities: {
    level: string;
    text: string;
  }[];
};

export type TPairStat = {
  [key: string]: number;
};

export type TPairStats = {
  buyTxn: TPairStat;
  buyVolume: TPairStat;
  buyer: TPairStat;
  maker: TPairStat;
  pairId: string;
  percent: TPairStat;
  sellTxn: TPairStat;
  sellVolume: TPairStat;
  seller: TPairStat;
  totalNumTxn: TPairStat;
  volume: TPairStat;
};

export type TPairTransaction = {
  isNew?: boolean;
  baseAmount: string;
  blockNumber: number;
  hash: string;
  index?: number;
  token?: string;
  maker: {
    address: string;
    traderCategory: string;
  };
  pairId: string;
  price: string;
  priceUsd: string;
  quoteAmount: string;
  timestamp: number;
  totalUsd: string;
  tradingType: TradingType;
  version: number;
};

export type TPairsStatsSocket = {
  pairId: string;
  percent: TPairStat;
  volume: TPairStat;
  marketCapPercent: TPairStat;
  maker: TPairStat;
  buyer: TPairStat;
  seller: TPairStat;
  buyVolume: TPairStat;
  sellVolume: TPairStat;
  buyTxn: TPairStat;
  sellTxn: TPairStat;
  totalNumTxn: TPairStat;
  volumeUsdPercent: TPairStat;
  liquidityUsdPercent: TPairStat;
  txPercent: TPairStat;
  uniqueMaker: TPairStat;
};

export type TCandle = {
  close: string;
  high: string;
  low: string;
  open: string;
  timestamp: number;
  volumeBase: string;
  volumeQuote: string;
  volumeUsd: string;
};
