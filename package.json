{"name": "printmeme-evm-interface", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@cetusprotocol/sui-clmm-sdk": "^1.1.3", "@mysten/dapp-kit": "^0.16.16", "@mysten/sui": "^1.36.1", "@next/third-parties": "^15.1.4", "@number-flow/react": "^0.4.4", "@privy-io/react-auth": "^2.4.3", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.62.8", "async-retry": "^1.3.3", "axios": "^1.7.9", "bignumber.js": "^9.1.2", "bn.js": "^5.2.1", "bs58": "^6.0.0", "clsx": "^2.1.1", "comma-number": "^2.1.0", "copy-to-clipboard": "^3.3.3", "ethers": "^6.14.4", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "14.2.21", "next-auth": "^4.24.11", "rc-collapse": "^4.0.0", "rc-tooltip": "^6.3.2", "react": "^18", "react-datepicker": "^8.4.0", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-fast-marquee": "^1.6.5", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-modal": "^3.16.1", "react-number-format": "^5.4.3", "react-redux": "^9.2.0", "react-responsive": "^10.0.0", "react-textarea-autosize": "^8.5.6", "react-toastify": "^11.0.2", "react-virtuoso": "^4.12.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "turbos-clmm-sdk": "^3.6.3", "uuid": "^11.0.4", "viem": "^2.31.7"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/async-retry": "^1.4.9", "@types/comma-number": "^2.1.2", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "dotenv": "^16.4.7", "eslint": "^8", "eslint-config-next": "14.2.21", "pino-pretty": "^13.0.0", "postcss": "^8", "prettier": "^2.8.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}