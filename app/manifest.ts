import type { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Printme<PERSON>',
    short_name: 'Printmeme',
    description: 'Printmeme',
    start_url: '/',
    display: 'standalone',
    background_color: '#000000',
    theme_color: '#000000',
    id: new Date().toISOString(),
    icons: [
      {
        src: '/pwa/favicon192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/pwa/favicon512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'any',
      },
    ],
    screenshots: [
      {
        src: '/pwa/screenshotPC.png',
        sizes: '1280x800',
        type: 'image/png',
      },
      {
        src: '/pwa/screenshotMobile.png',
        sizes: '375x667',
        type: 'image/png',
      },
    ],
  };
}
